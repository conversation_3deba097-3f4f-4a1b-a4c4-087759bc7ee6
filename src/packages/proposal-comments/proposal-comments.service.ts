import { Injectable, NotFoundException } from '@nestjs/common';
import {
  ProposalCommentsRepository,
  ProposalComments,
} from '@ghq-abi/northstar-domain';

@Injectable()
export class ProposalCommentsService {
  constructor(
    private readonly proposalCommentsRepository: ProposalCommentsRepository,
  ) {}

  async getProposalComments(
    proposalId: string,
    pageNumber: number,
    pageSize: number,
  ): Promise<{
    data: ProposalComments[];
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
  }> {
    return this.proposalCommentsRepository.findMessagesWithPagination(
      proposalId,
      pageNumber,
      pageSize,
    );
  }

  async createProposalComment(
    proposalId: string,
    comment: string,
    employeeId: string,
  ): Promise<ProposalComments> {
    try {
      return await this.proposalCommentsRepository.createMessage(
        proposalId,
        comment,
        employeeId,
      );
    } catch (err) {
      if (err.name === 'NotFoundException') {
        throw new NotFoundException(err.message);
      }
      throw err;
    }
  }

  async deleteProposalComment(messageId: string) {
    return this.proposalCommentsRepository.deleteMessage(messageId);
  }
}
