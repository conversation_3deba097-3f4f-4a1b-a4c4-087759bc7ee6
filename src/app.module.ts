import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import typeorm from './configs/typeorm.config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExtraLogMiddleware } from './middlewares/extra-log.middleware';
import { DataSourceOptions } from 'typeorm';
import { TargetsModule } from './packages/targets/targets.module';
import { ProposalModule } from './packages/proposal/proposal.module';
import { TargetTypeModule } from './packages/target-type/target-type.module';
import { EmployeeModule } from './packages/employee/employee.module';
import { ProposalCommentsModule } from './packages/proposal-comments/proposal-comments.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [typeorm],
    }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (
        configService: ConfigService,
      ): Promise<DataSourceOptions> =>
        configService.get<DataSourceOptions>('typeorm'),
    }),
    TargetsModule,
    ProposalModule,
    TargetTypeModule,
    EmployeeModule,
    ProposalCommentsModule,
  ],
  controllers: [AppController],
  providers: [AppService, ExtraLogMiddleware],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(ExtraLogMiddleware).exclude({
      method: RequestMethod.GET,
      path: '*',
    });
  }
}
